/**
 * 会员模型
 */
const db = require('../config/db');

class VipMember {
  /**
   * 获取会员等级列表
   * @returns {Promise<Array>} 会员等级列表
   */
  static async getVipLevels() {
    return await db.query('SELECT * FROM vip_levels ORDER BY price ASC');
  }

  /**
   * 获取指定等级的会员信息
   * @param {string} levelCode 会员等级编码
   * @returns {Promise<Object>} 会员等级信息
   */
  static async getVipLevelByCode(levelCode) {
    const levels = await db.query('SELECT * FROM vip_levels WHERE level_code = ?', [levelCode]);
    return levels.length > 0 ? levels[0] : null;
  }

  /**
   * 获取会员等级权益
   * @param {string} levelCode 会员等级编码
   * @returns {Promise<Array>} 会员权益列表
   */
  static async getVipBenefits(levelCode) {
    return await db.query(
      'SELECT * FROM vip_benefits WHERE level_code = ? ORDER BY sort_order ASC',
      [levelCode]
    );
  }

  /**
   * 获取所有会员等级权益
   * @returns {Promise<Array>} 所有会员权益列表
   */
  static async getAllVipBenefits() {
    return await db.query(
      'SELECT * FROM vip_benefits ORDER BY level_code, sort_order ASC'
    );
  }

  /**
   * 获取用户会员信息
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 用户会员信息
   */
  static async getUserVipInfo(userId) {
    const userVip = await db.query(`
      SELECT uv.*, vl.level_name, vl.description, vl.icon
      FROM user_vip uv
      JOIN vip_levels vl ON uv.level_code = vl.level_code
      WHERE uv.user_id = ?
    `, [userId]);

    if (userVip.length === 0) {
      // 如果用户没有会员记录，则创建一个免费会员记录
      const defaultExpireDate = new Date();
      defaultExpireDate.setFullYear(defaultExpireDate.getFullYear() + 10); // 设置为10年后

      await db.query(
        'INSERT INTO user_vip (user_id, level_code, start_time, expire_time) VALUES (?, ?, NOW(), ?)',
        [userId, 'free', defaultExpireDate]
      );

      return {
        user_id: userId,
        level_code: 'free',
        level_name: '普通用户',
        start_time: new Date(),
        expire_time: defaultExpireDate,
        is_active: true
      };
    }

    // 检查会员是否有效
    const now = new Date();
    const vipInfo = userVip[0];
    const expireTime = new Date(vipInfo.expire_time);

    // 如果已过期，更新状态
    if (expireTime < now && vipInfo.is_active) {
      await db.query(
        'UPDATE user_vip SET is_active = ? WHERE id = ?',
        [false, vipInfo.id]
      );
      vipInfo.is_active = false;
    }

    return vipInfo;
  }

  /**
   * 更新用户会员信息
   * @param {string} userId 用户ID
   * @param {string} levelCode 会员等级编码
   * @param {Date} expireTime 过期时间
   * @param {string} paymentId 支付ID
   * @returns {Promise<Object>} 更新后的用户会员信息
   */
  static async updateUserVip(userId, levelCode, expireTime, paymentId = null) {
    // 检查用户是否已有会员记录
    const userVip = await db.query('SELECT * FROM user_vip WHERE user_id = ?', [userId]);

    if (userVip.length === 0) {
      // 创建新记录
      await db.query(
        'INSERT INTO user_vip (user_id, level_code, start_time, expire_time, payment_id) VALUES (?, ?, NOW(), ?, ?)',
        [userId, levelCode, expireTime, paymentId]
      );
    } else {
      // 更新现有记录
      await db.query(
        'UPDATE user_vip SET level_code = ?, expire_time = ?, payment_id = ?, is_active = true, updated_at = NOW() WHERE user_id = ?',
        [levelCode, expireTime, paymentId, userId]
      );
    }

    return await this.getUserVipInfo(userId);
  }

  /**
   * 获取会员产品列表（包含权益）
   * @returns {Promise<Array>} 会员产品列表
   */
  static async getVipProducts() {
    const levels = await this.getVipLevels();

    // 获取每个等级的权益
    const products = [];
    for (const level of levels) {
      const benefits = await this.getVipBenefits(level.level_code);

      products.push({
        id: level.id,
        code: level.level_code,
        name: level.level_name,
        description: level.description,
        price: level.price,
        duration: level.duration,
        icon: level.icon,
        benefits: benefits.map(b => b.benefit_name),
        buttonText: level.level_code === 'free' ? '免费' : (
          level.level_code === 'annual' ? '会员续费' :
          level.level_code === 'super' ? '立即开通' : '升级'
        )
      });
    }

    return products;
  }

  /**
   * 格式化用户会员信息用于前端显示
   * @param {Object} vipInfo 用户会员信息
   * @returns {Object} 格式化后的会员信息
   */
  static formatVipInfoForDisplay(vipInfo) {
    if (!vipInfo) return null;

    const expireDate = vipInfo.expire_time instanceof Date
      ? vipInfo.expire_time
      : new Date(vipInfo.expire_time);

    const year = expireDate.getFullYear();
    const month = expireDate.getMonth() + 1;
    const day = expireDate.getDate();

    return {
      vipLevel: vipInfo.level_name,
      levelCode: vipInfo.level_code,
      isActive: vipInfo.is_active,
      expireDate: `${year}年${month}月${day}日`,
      expireTimestamp: expireDate.getTime()
    };
  }
}

module.exports = VipMember;
