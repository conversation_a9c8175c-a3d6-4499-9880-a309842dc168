// API 环境配置
const API_CONFIG = {
  // 本地开发环境
  local: {
    baseUrl: 'http://localhost:3001'
  },
  // 开发环境
  dev: {
    baseUrl: 'http://localhost:3001'
  },
  // 生产环境（云托管公网地址）
  prod: {
    baseUrl: 'https://lieyouqi-158837-8-1258719867.sh.run.tcloudbase.com'
  },
  // 备用环境
  backup: {
    baseUrl: 'http://localhost:3001'
  }
};

// 默认环境
const DEFAULT_ENV = 'dev';

// 获取当前API环境
const getApiEnv = () => {
  return wx.getStorageSync('apiEnv') || DEFAULT_ENV;
};

// 当前环境
let CURRENT_ENV = getApiEnv();

// API 基础 URL - 动态判断环境
const getBaseUrl = () => {
  // 统一返回云托管公网地址，解决开发工具/预览/真机无法访问本地服务问题
  return API_CONFIG.prod.baseUrl;
};

// 设置API环境
const setApiEnv = (env) => {
  if (!API_CONFIG[env]) {
    return false;
  }

  // 更新当前环境
  wx.setStorageSync('apiEnv', env);

  // 需要刷新页面以应用新的BASE_URL
  wx.showModal({
    title: '环境已切换',
    content: `API环境已切换为: ${env}，需要重启小程序以应用更改`,
    showCancel: false
  });

  return true;
};

// 云托管相关配置
const envId = 'prod-5geioww562624006'; // 微信云托管环境ID
const serviceName = 'lieyouqi'; // 云托管服务名

// 请求方法
const request = (url, method = 'GET', data = {}) => {
  // 构建请求头
  const headers = {
    'content-type': 'application/json'
  };

  // 获取token
  const token = wx.getStorageSync('token');

  // 需要认证的接口都自动加token
  const needAuth = (
    url.includes('/posts') ||
    url.includes('/cart') ||
    url.includes('/users') ||
    url.includes('/user') ||
    url.includes('/messages') ||
    url.includes('/message') ||
    url.includes('/groups') ||
    url.includes('/group')
  );

  // 对于POST /api/posts 请求，强制添加认证头
  const isCreatePost = (method === 'POST' && url === '/api/posts');

  if ((needAuth || isCreatePost) && token) {
    headers['Authorization'] = `Bearer ${token}`;

    // 对于发帖请求，额外添加用户ID到请求头
    if (isCreatePost) {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        headers['X-User-ID'] = userInfo.id;
      }
    }
  }

  // GET请求时将data拼接到URL query string
  let finalUrl = url;
  if (method === 'GET' && data && Object.keys(data).length > 0) {
    let processedData = { ...data };

    // 处理筛选相关参数
    if (url.includes('/api/posts')) {
      console.log('处理筛选参数');

      // 检查是否有主题筛选
      const hasTopicFilter = data.topics &&
        ((typeof data.topics === 'string' && data.topics.length > 0) ||
         (Array.isArray(data.topics) && data.topics.length > 0));

      // 特别处理不同主题参数的情况
      let topicNames = [];

      // 1. 处理topics参数
      if (data.topics) {
        if (typeof data.topics === 'string' && data.topics.length > 0) {
          topicNames = data.topics.split(',').map(t => t.trim()).filter(t => t);
        } else if (Array.isArray(data.topics)) {
          topicNames = data.topics.filter(t => t && t.length > 0);
        }
      }

      // 2. 处理topicNames参数
      if (data.topicNames && topicNames.length === 0) {
        if (Array.isArray(data.topicNames)) {
          topicNames = data.topicNames.filter(t => t && t.length > 0);
        }
      }

      // 3. 处理selectedTopicNames参数
      if (data.selectedTopicNames && topicNames.length === 0) {
        if (Array.isArray(data.selectedTopicNames)) {
          topicNames = data.selectedTopicNames.filter(t => t && t.length > 0);
        }
      }

      // 如果有主题参数，则在请求中使用这些主题
      if (topicNames.length > 0) {
        processedData.topics = topicNames.join(',');
        console.log('使用主题筛选:', processedData.topics);
      }

      // 检查是否有地区筛选
      const hasRegionFilter = data.region &&
        ((typeof data.region === 'string' && data.region !== '全部' && data.region !== '全国') ||
         (Array.isArray(data.region) && data.region.some(r => r !== '全部')));

      // 特别处理地区参数
      if (data.region) {
        // 如果是数组，则过滤掉“全部”并合并
        if (Array.isArray(data.region)) {
          const filteredRegions = data.region.filter(r => r !== '全部' && r !== '全国');
          if (filteredRegions.length > 0) {
            processedData.region = filteredRegions.join(',');
            console.log('处理地区筛选(数组):', processedData.region);
          } else {
            // 如果全部是“全部”，则不传地区参数
            delete processedData.region;
          }
        } else if (typeof data.region === 'string') {
          // 如果是字符串，则检查是否为“全部”
          if (data.region !== '全部' && data.region !== '全国') {
            processedData.region = data.region;
            console.log('处理地区筛选(字符串):', processedData.region);
          } else {
            delete processedData.region;
          }
        }
      }

      // 1. 当选择了地区，没有选择主题时，仅作地区筛选，不作主题筛选
      if (hasRegionFilter && !hasTopicFilter) {
        console.log('仅作地区筛选，不筛选主题');
        processedData.onlyFilterByRegion = 'true';
        processedData.useRegionFilter = 'true';

        // 清除主题相关参数
        delete processedData.topics;
        delete processedData.topicIds;
        delete processedData.topicFilter;
      }

      // 2. 当选择了地区，也选了主题时，同时筛选地区和主题
      if (hasRegionFilter && hasTopicFilter) {
        console.log('同时筛选地区和主题');
        processedData.useRegionFilter = 'true';
        processedData.topicFilterType = 'OR'; // 主题是"或"关系
        processedData.topicAndRegionRelation = 'AND'; // 主题与地区是"与"关系
      }

      // 3. 当不选择地区，仅选择主题时，不筛选地区，只筛选主题
      if (!hasRegionFilter && hasTopicFilter) {
        console.log('不筛选地区，只筛选主题');
        processedData.topicFilterType = 'OR'; // 主题是"或"关系

        // 清除地区筛选相关参数
        delete processedData.region;
        delete processedData.regionFilter;
        delete processedData.useRegionFilter;
      }

      // 4. 无论是否选择地区，当选择了多个主题的，主题符合任意一个即可
      if (hasTopicFilter) {
        console.log('设置主题为或关系');
        processedData.topicFilterType = 'OR';
      }
    }

    // 构建查询字符串
    const queryString = Object.keys(processedData)
      .map(key => {
        const value = typeof processedData[key] === 'object'
          ? JSON.stringify(processedData[key])
          : processedData[key];
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      })
      .join('&');

    finalUrl = url + (url.includes('?') ? '&' : '?') + queryString;
  }

  return new Promise((resolve, reject) => {
    wx.cloud.callContainer({
      config: {
        env: envId
      },
      path: finalUrl,
      method,
      header: {
        'X-WX-SERVICE': serviceName,
        ...headers
      },
      data: method === 'GET' ? undefined : data,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 对于错误状态码，创建一个包含错误信息的Error对象
          const errorData = res.data || {};
          const errorMessage = errorData.message || `请求失败，状态码: ${res.statusCode}`;
          const error = new Error(errorMessage);
          error.statusCode = res.statusCode;
          error.data = errorData;
          reject(error);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 商品相关 API
const productApi = {
  // 获取商品列表
  getProducts: (params = {}) => {
    return request('/api/products', 'GET', params);
  },

  // 获取商品详情
  getProductById: (id) => {
    return request(`/api/products/${id}`);
  },

  // 获取商品分类
  getCategories: () => {
    return request('/api/products/categories');
  },

  // 获取商城分类
  getShopCategories: () => {
    return request('/api/products/shop/categories');
  },

  // 获取商城子分类
  getShopSubCategories: (parentId) => {
    return request('/api/products/shop/subcategories', 'GET', { parentId });
  },

  // 获取轮播图
  getBanners: () => {
    return request('/api/products/banners');
  }
};

// 购物车相关 API
const cartApi = {
  // 获取购物车商品
  getCartItems: () => {
    return request('/api/cart');
  },

  // 添加商品到购物车
  addToCart: (productId, quantity = 1) => {
    return request('/api/cart', 'POST', { productId, quantity });
  },

  // 更新购物车商品
  updateCartItem: (id, quantity) => {
    return request(`/api/cart/${id}`, 'PUT', { quantity });
  },

  // 删除购物车商品
  removeCartItem: (id) => {
    return request(`/api/cart/${id}`, 'DELETE');
  },

  // 批量删除购物车商品
  batchRemoveCartItems: (ids) => {
    return request('/api/cart', 'DELETE', { ids });
  }
};

// 用户相关 API
const userApi = {
  // 用户登录
  login: (code) => {
    return request('/api/user/login', 'POST', { code });
  },

  // 手机号登录
  loginByPhone: (phone, code) => {
    console.log('API调用 - loginByPhone 参数:', { phone, code });
    return request('/api/users/login/phone', 'POST', { phone, code });
  },

  // 账号密码登录
  loginByAccount: (username, password) => {
    console.log('API调用 - loginByAccount 参数:', { username, password: '***' });
    // 确保使用正确的路径
    return request('/api/users/login', 'POST', { username, password });
  },

  // 微信登录
  loginByWechat: (code) => {
    return request('/api/users/login/wechat', 'POST', { code }).then(res => {
      if (res.success && res.data && res.data.token) {
        wx.setStorageSync('token', res.data.token);
        if (res.data.user || res.data.userInfo) {
          wx.setStorageSync('userInfo', res.data.user || res.data.userInfo);
        }
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLogin = true;
          app.globalData.userInfo = res.data.user || res.data.userInfo;
        }
      }
      return res;
    });
  },

  // 获取用户信息
  getUserInfo: (userId) => {
    // 兼容后端有的用id，有的用userId
    let url = '/api/users/info';
    if (userId) {
      url += userId.length > 10 ? `?userId=${userId}` : `?id=${userId}`;
    }
    return request(url);
  },

  // 获取用户统计信息
  getUserStats: () => {
    return request('/api/users/stats');
  },

  // 发送验证码
  sendVerifyCode: (phone) => {
    console.log('API调用 - sendVerifyCode 参数:', { phone });
    return request('/api/users/send-code', 'POST', { phone });
  },

  // 更新用户信息
  updateUserInfo: (data) => {
    console.log('API调用 - updateUserInfo 参数:', data);
    // 添加调试信息
    const token = wx.getStorageSync('token');
    console.log('更新用户信息时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');
    // 确保使用正确的路径
    return request('/api/users/info', 'PUT', data)
      .then(res => {
        console.log('更新用户信息响应:', res);
        return res;
      })
      .catch(err => {
        console.error('更新用户信息失败:', err);
        throw err;
      });
  },

  // 更新密码
  updatePassword: (oldPassword, newPassword) => {
    console.log('API调用 - updatePassword', { oldPassword: '***', newPassword: '***' });
    // 添加调试信息
    const token = wx.getStorageSync('token');
    console.log('修改密码时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');
    return request('/api/users/password', 'PUT', { oldPassword, newPassword })
      .then(res => {
        console.log('修改密码响应:', res);
        return res;
      })
      .catch(err => {
        console.error('修改密码请求失败:', err);
        throw err;
      });
  },

  // 绑定微信
  bindWechat: (code, userInfo) => {
    console.log('API调用 - bindWechat');
    return request('/api/users/bind/wechat', 'POST', { code, userInfo });
  },

  // 解绑微信
  unbindWechat: () => {
    console.log('API调用 - unbindWechat');
    return request('/api/users/unbind/wechat', 'POST');
  },

  // 通过手机号重置密码
  resetPasswordByPhone: (phone, code, newPassword) => {
    console.log('API调用 - resetPasswordByPhone 参数:', { phone, code: '***', newPassword: '***' });
    return request('/api/users/reset-password', 'POST', { phone, code, newPassword })
      .then(res => {
        console.log('重置密码响应:', res);
        return res;
      })
      .catch(err => {
        console.error('重置密码请求失败:', err);
        throw err;
      });
  },

  followUser: (followingId) => {
    return request('/api/users/follow', 'POST', { followingId });
  },

  unfollowUser: (followingId) => {
    return request('/api/users/unfollow', 'POST', { followingId });
  },

  checkFollowStatus: (followingId) => {
    return request('/api/users/follow/status', 'GET', { followingId });
  },

  getMyFollowList: () => request('/api/users/my-follow', 'GET'),

  // 获取我的粉丝列表
  getMyFansList: () => {
    return request('/api/users/my-fans');
  },

  // 获取我的推广用户列表
  getMyPromotionList: () => {
    return request('/api/users/my-promotion');
  },

  // 获取我的评论记录
  getMyComments: () => {
    return request('/api/users/my-comments');
  },

  // 获取指定帖子评论
  getPostComments: (postId) => {
    return request('/api/users/post-comments', 'GET', { postId });
  },

  // 发表评论
  addPostComment: (postId, content) => {
    return request('/api/users/post-comment', 'POST', { postId, content });
  },
};

// 信息流相关 API
const postApi = {
  // 获取信息流
  getPosts: (params = {}) => {
    console.log('API调用 - getPosts 参数:', params);
    console.log('获取帖子数据 - 这个操作不需要登录');
    return request('/api/posts', 'GET', params);
  },

  // 创建信息
  createPost: (data) => {
    // 确保请求中包含用户ID
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      // 如果用户信息存在，确保有id字段
      if (!userInfo.id && userInfo._id) {
        console.log('发帖时用户信息中使用_id替代id，正在规范化');
        userInfo.id = userInfo._id;
        // 更新存储
        wx.setStorageSync('userInfo', userInfo);
      }

      // 添加用户ID到请求数据中
      if (userInfo.id) {
        data.userId = userInfo.id;
      }
    }

    console.log('发帖请求数据:', data);
    return request('/api/posts', 'POST', data);
  },

  // 点赞信息
  likePost: (id) => {
    return request(`/api/posts/${id}/like`, 'POST');
  },

  // 获取热门话题
  getHotTopics: () => {
    return request('/api/posts/hot-topics');
  },

  // 获取轮播图
  getBanners: () => {
    return request('/api/posts/banners');
  },

  // 删除帖子
  deletePost: (id) => {
    return request(`/api/posts/${id}`, 'DELETE');
  },

  // 隐藏/显示帖子
  togglePostVisibility: (id, isHidden) => {
    return request(`/api/posts/${id}/visibility`, 'PUT', { isHidden });
  }
};

// 消息相关 API
const messageApi = {
  // 获取消息
  getMessages: (params = {}) => {
    console.log('API调用 - getMessages 参数:', params);

    // 如果有targetUserId参数，说明是获取与特定用户的聊天记录
    if (params.targetUserId) {
      // 修改为使用正确的路由
      return request('/api/message/list', 'GET', params);
    } else {
      // 否则获取所有消息
      return request('/api/message/list', 'GET', params);
    }
  },

  // 删除消息
  deleteMessage: (id) => {
    return request(`/api/messages/${id}`, 'DELETE');
  },

  // 标记消息为已读
  markMessageRead: (id) => {
    return request(`/api/messages/${id}/read`, 'PUT');
  },

  // 发送消息
  sendMessage: (receiverId, content, type = 'text') => {
    console.log('API调用 - sendMessage 参数:', { receiverId, content, type });
    // 修改为使用正确的路由
    return request('/api/message/send', 'POST', { receiverId, content, type });
  },

  // 获取最近聊天列表
  getRecentChats: () => {
    // 修改为使用正确的路由
    return request('/api/message/recent', 'GET');
  }
};

// 群聊相关API
const groupApi = {
  // 获取我的群聊列表
  getMyGroups: function() {
    return request('/api/group/my-groups', 'GET');
  },

  // 获取公开群聊列表
  getPublicGroups: function(page = 1, pageSize = 20) {
    return request('/api/group/public-groups', 'GET', { page, pageSize });
  },

  // 创建群聊
  createGroup: function(data) {
    return request('/api/group', 'POST', data);
  },

  // 获取群组详情
  getGroupDetail: function(groupId) {
    return request(`/api/group/${groupId}`, 'GET');
  },

  // 获取群组成员列表
  getGroupMembers: function(groupId) {
    return request(`/api/group/${groupId}/members`, 'GET');
  },

  // 加入群组
  joinGroup: function(groupId) {
    return request(`/api/group/${groupId}/join`, 'POST');
  },

  // 退出群组
  leaveGroup: function(groupId) {
    return request(`/api/group/${groupId}/leave`, 'POST');
  },

  // 获取群消息历史
  getGroupMessages: function(groupId, page = 1, pageSize = 20) {
    return request(`/api/group/${groupId}/messages`, 'GET', { page, pageSize });
  },

  // 发送群消息
  sendGroupMessage: function(groupId, content, type = 'text') {
    return request(`/api/group/${groupId}/messages`, 'POST', { content, type });
  },

  // 更新群公告
  updateAnnouncement: function(groupId, announcement) {
    return request(`/api/group/${groupId}/announcement`, 'PUT', { announcement });
  },

  // 更新群组信息
  updateGroupInfo: function(groupId, updateData) {
    return request(`/api/group/${groupId}/info`, 'PUT', updateData);
  }
};

// 积分相关 API
const pointsApi = {
  // 获取积分规则和获取方式
  getPointsConfig: () => {
    return request('/api/points/config');
  },
  // 获取用户积分
  getUserPoints: (userId) => {
    return request(`/api/points/user${userId ? `?userId=${userId}` : ''}`);
  },
  // 获取用户积分记录
  getUserPointsRecords: (params = {}) => {
    const { userId, limit = 10, offset = 0 } = params;
    return request(`/api/points/user/records?limit=${limit}&offset=${offset}${userId ? `&userId=${userId}` : ''}`);
  },
  // 更新用户积分
  updateUserPoints: (changeAmount, event) => {
    return request('/api/points/user/update', {
      method: 'POST',
      data: { changeAmount, event }
    });
  }
};

// VIP会员相关 API
const vipApi = {
  // 获取会员等级列表
  getVipLevels: () => {
    return request('/api/vip/levels');
  },

  // 获取会员权益
  getVipBenefits: (levelCode) => {
    return request(`/api/vip/benefits${levelCode ? `?levelCode=${levelCode}` : ''}`);
  },

  // 获取用户会员信息
  getUserVipInfo: (userId) => {
    return request(`/api/vip/user${userId ? `?userId=${userId}` : ''}`);
  },

  // 获取会员产品列表
  getVipProducts: () => {
    return request('/api/vip/products');
  },

  // 更新用户会员信息
  updateUserVip: (data) => {
    return request('/api/vip/user/update', 'POST', data);
  }
};

// 设置相关 API
const settingsApi = {
  // 获取公司联系方式
  getCompanyContact: () => {
    // 先尝试获取第一条记录，如果失败则尝试获取列表
    return request('/api/company/info')
      .catch(err => {
        console.log('尝试获取公司信息失败，尝试获取列表:', err);
        return request('/api/company/list').then(res => {
          if (res.success && res.data && res.data.length > 0) {
            return {
              success: true,
              data: res.data[0] // 返回第一条记录
            };
          }
          throw new Error('没有找到公司信息');
        });
      });
  },

  // 提交客服留言
  submitMessage: (data) => {
    return request('/api/system/feedback', 'POST', data);
  }
};

// 重置API配置到默认值
const resetApiConfig = () => {
  wx.removeStorageSync('apiEnv');
  CURRENT_ENV = DEFAULT_ENV;
  console.log('API配置已重置为默认环境:', DEFAULT_ENV);
};

/**
 * 上传单个文件到微信云存储，返回Promise<fileID>
 * @param {string} filePath 本地临时文件路径
 * @returns {Promise<string>} 云存储fileID
 */
const uploadFile = (filePath) => {
  return new Promise((resolve, reject) => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
    const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const cloudPath = `group_covers/${timestamp}_${random}.${fileExt}`;
    wx.cloud.uploadFile({
      cloudPath,
      filePath,
      config: { env: envId },
      success: res => {
        if (res.fileID) {
          resolve(res.fileID);
        } else {
          reject(new Error('上传失败'));
        }
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

module.exports = {
  productApi,
  cartApi,
  userApi,
  postApi,
  messageApi,
  groupApi,
  pointsApi,
  vipApi,
  settingsApi,
  // 导出API环境控制函数
  setApiEnv,
  getApiEnv,
  // 导出环境配置
  API_CONFIG,
  // 导出重置函数
  resetApiConfig,
  uploadFile // 新增导出
};
