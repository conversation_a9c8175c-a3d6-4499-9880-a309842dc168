<view class="vip-center-container">
  <!-- 用户信息区 -->  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
    <view class="user-details">
      <view class="nickname">{{userInfo.nickName || '昵称'}}</view>
      <view class="user-id">ID: {{userInfo.id || 'XXXXXXXXXX'}}</view>
    </view>
    <view class="vip-info" wx:if="{{!loading.vipInfo}}">
      <view class="vip-label">{{vipInfo.level_name || '普通用户'}}</view>
      <view class="vip-expire" wx:if="{{vipInfo.expireDate}}">有效期至{{vipInfo.expireDate}}</view>
      <view class="vip-expire" wx:else>暂无会员权益</view>
    </view>
    <view class="vip-info" wx:else>
      <view class="vip-loading">正在加载...</view>
    </view>
  </view>
  <!-- 轮播图区 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#1E6A9E">
      <block wx:for="{{banners}}" wx:key="index">
        <swiper-item>
          <image class="banner-image" src="{{item}}" mode="aspectFill"></image>
          <view class="banner-title" wx:if="{{bannerTitles[index]}}">{{bannerTitles[index]}}</view>
        </swiper-item>
      </block>
    </swiper>
  </view>  <!-- 会员权益卡片区 -->  <view class="vip-benefit-section">
    <!-- 加载状态显示 -->
    <view class="loading-container" wx:if="{{loading.vipProducts || loading.vipBenefits}}">
      <view class="loading-spinner"></view>
      <text>正在加载会员产品...</text>
    </view>

    <!-- 会员产品轮播 -->
    <swiper class="benefit-swiper" wx:else indicator-dots="true" circular="true" autoplay="{{false}}"
      current="{{currentProductIndex}}" bindchange="onProductSwiperChange"
      previous-margin="120rpx" next-margin="120rpx" display-multiple-items="1" style="height:630rpx;overflow:visible;">
      <block wx:for="{{vipProducts}}" wx:key="id">
        <swiper-item class="benefit-swiper-item">
          <view class="benefit-card {{currentProductIndex === index ? 'active' : ''}}">
            <view class="benefit-title">{{item.level_name}}</view>
            <view class="benefit-price">¥{{item.price}}/年</view>
            <view class="benefit-list">
              <view class="benefit-item" wx:for="{{item.benefits}}" wx:key="index" wx:for-item="benefit">
                {{benefit}}
              </view>
            </view>
            <button class="renew-btn" bindtap="handleVipAction">{{item.buttonText || '立即开通'}}</button>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <button class="more-vip-btn">更多会员产品</button>
</view>