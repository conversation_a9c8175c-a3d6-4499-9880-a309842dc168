.vip-center-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, #fff, #f8f9fa);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.vip-center-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #17637a, #1E6A9E, #17637a);
  z-index: 10;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 28rpx 24rpx;
  background: linear-gradient(135deg, #f5f7f9, #fff);
  border-bottom: 1rpx solid rgba(229, 229, 229, 0.8);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
}

.user-info::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 1rpx;
  background: linear-gradient(to right, transparent, rgba(23, 99, 122, 0.1), transparent);
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #17637a;
  margin-right: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user-id {
  font-size: 26rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.04);
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.vip-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.vip-label {
  font-size: 36rpx;
  color: #17637a;
  font-weight: bold;
  position: relative;
  padding-left: 40rpx;
}

.vip-label::before {
  content: '★';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #1E6A9E;
  font-size: 32rpx;
}

.vip-expire {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
  background: rgba(30, 106, 158, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 轮播图 */
.banner-section {
  padding: 0 15px 10px;
  background-color: #FFFFFF;
  margin-top: 10px;
  margin-bottom: 0;
  position: relative;
}

.banner-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 2rpx;
  background: linear-gradient(to right, transparent, rgba(30, 106, 158, 0.1), transparent);
}

.banner-swiper {
  width: 100%;
  height: 140px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
}

.banner-swiper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 2;
  pointer-events: none;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  display: block; /* 确保图片正确显示 */
  transform: scale(1.02);
  transition: transform 0.5s ease;
}

.wx-swiper-dot {
  width: 12rpx !important;
  height: 12rpx !important;
  border-radius: 6rpx !important;
  margin-left: 8rpx !important;
  margin-right: 8rpx !important;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
  color: #FFFFFF;
  font-size: 15px;
  line-height: 1.4;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
  z-index: 3;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.vip-benefit-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0 0 0;
  position: relative;
  padding: 15rpx 0;
  height: 650rpx; /* 进一步减小高度 */
  background-color: #f5f5f7;
  border-top: 1rpx solid #e8e8e8;
  border-bottom: 1rpx solid #e8e8e8;
  position: relative;
  overflow: hidden;
}

.vip-benefit-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10rpx;
  background: linear-gradient(to right, #17637a, #1E6A9E, #17637a);
  opacity: 0.15;
}
.benefit-swiper {
  width: 100%;
  height: 630rpx;
  min-height: 630rpx;
  background: transparent;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 24rpx;
  box-sizing: border-box;
  position: relative;
  padding: 10rpx 0;
}

/* 自定义轮播指示器样式 */
.benefit-swiper .wx-swiper-dots {
  margin-bottom: -10rpx !important;
}

.benefit-swiper .wx-swiper-dot {
  width: 16rpx;
  height: 16rpx;
  background: rgba(30, 106, 158, 0.2) !important;
}

.benefit-swiper .wx-swiper-dot-active {
  width: 24rpx;
  background: rgba(30, 106, 158, 0.8) !important;
  border-radius: 8rpx;
}
.benefit-swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  height: 630rpx;
  padding: 10rpx 0;
}
.benefit-card {
  width: 85%;
  height: 85%; /* 增加卡片高度 */
  background: #fff;
  border-radius: 32rpx; /* 增大圆角 */
  box-shadow: 0 10rpx 30rpx rgba(23,99,122,0.15), 0 4rpx 10rpx rgba(0,0,0,0.08);
  border: none; /* 去掉边框 */
  padding: 30rpx 32rpx 25rpx; /* 减少上下内边距 */
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  min-height: 450rpx;
  overflow-y: auto;
  backdrop-filter: blur(8px);
  background-image: linear-gradient(to bottom right, rgba(255,255,255,0.9), rgba(255,255,255,1));
}
.benefit-card:not(.active) {
  opacity: 0.7;
  transform: scale(0.85);
  background-color: #f9f9f9;
  height: 85%; /* 与激活状态保持一致 */
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 确保非激活状态下的标题样式一致 */
.benefit-card:not(.active) .benefit-title {
  font-size: 36rpx; /* 稍微小一点 */
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
}

/* 确保非激活状态下的价格样式一致 */
.benefit-card:not(.active) .benefit-price {
  font-size: 26rpx; /* 稍微小一点 */
  margin: 6rpx 0 10rpx 0;
}

/* 确保非激活状态下的权益列表样式一致 */
.benefit-card:not(.active) .benefit-list {
  margin: 6rpx 0 18rpx;
}

/* 确保非激活状态下的权益项样式一致 */
.benefit-card:not(.active) .benefit-item {
  font-size: 26rpx; /* 稍微小一点 */
  margin-bottom: 14rpx;
}

/* 确保非激活状态下的按钮样式一致 */
.benefit-card:not(.active) .renew-btn {
  font-size: 26rpx; /* 稍微小一点 */
  padding: 12rpx 0;
  margin-top: 12rpx;
  margin-bottom: 8rpx;
}

.benefit-title {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 18rpx;
  color: #17637a;
  text-align: center;
  position: relative;
  padding-bottom: 12rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
}

.benefit-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, rgba(23,99,122,0.6), rgba(30,106,158,0.8), rgba(23,99,122,0.6));
  border-radius: 2rpx;
}
.benefit-list {
  width: 100%;
  margin: 8rpx 0 20rpx;
  flex: 1;
  overflow: visible;
}
.benefit-item {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 46rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.benefit-item::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #1E6A9E;
  font-weight: bold;
  width: 36rpx;
  height: 36rpx;
  background: rgba(30, 106, 158, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}
.renew-btn {
  width: 75%;
  margin-top: 15rpx;
  margin-bottom: 10rpx;
  background: #17637a;
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
  padding: 14rpx 0;
  transition: all 0.3s;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  box-shadow: 0 6rpx 12rpx rgba(23,99,122,0.2);
  letter-spacing: 2rpx;
  font-weight: 500;
  overflow: hidden;
}

.renew-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255,255,255,0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.benefit-card.active .renew-btn {
  background: #1E6A9E;
  box-shadow: 0 8rpx 16rpx rgba(30, 106, 158, 0.35);
}

.benefit-card.active .renew-btn::after {
  transform: translateX(100%);
}

.benefit-card.active .benefit-title {
  color: #1E6A9E;
}


.benefit-card.active {
  transform: scale(0.98);
  height: 85%; /* 与非激活状态保持一致 */
  box-shadow: 0 16rpx 36rpx rgba(23, 99, 122, 0.25), 0 6rpx 16rpx rgba(23, 99, 122, 0.15);
  border: none;
  background: #fff;
  z-index: 5;
  position: relative;
  background-image: linear-gradient(135deg, #fff, #f9fcff);
}

.benefit-card.active::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 34rpx;
  box-shadow: 0 0 0 8rpx rgba(30, 106, 158, 0.08);
  pointer-events: none;
}

.benefit-card.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(to right, #17637a, #1E6A9E);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}
.more-vip-btn {
  width: 80%;
  margin: 30rpx auto 40rpx auto;
  background: rgba(255, 255, 255, 0.9);
  color: #17637a;
  font-size: 32rpx;
  border: 2rpx solid rgba(23,99,122,0.6);
  border-radius: 36rpx;
  padding: 18rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(23,99,122,0.08);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.more-vip-btn:active {
  transform: scale(0.98);
  background: rgba(23,99,122,0.05);
}

/* 加载状态样式 */
.vip-loading {
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
  padding: 4rpx 12rpx;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  color: #888;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(30, 106, 158, 0.1);
  border-radius: 50%;
  border-top-color: #1E6A9E;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 价格样式 */
.benefit-price {
  font-size: 28rpx;
  color: #e74c3c;
  margin: 8rpx 0 12rpx 0; /* 减少上下间距 */
  font-weight: bold;
  text-align: center;
}